import React from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';

import { QRCode, SearchIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import ModalDatePicker from '@/components/ui/modal-date-picker';
import ModalDropdown from '@/components/ui/modal-dropdown';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';
import { SEXES, SPECIES, WEIGHT_UNITS } from '@/constants/pets';

import { PetEditDetailsProps } from './services';
import styles from './styles';

const PetEditDetailsComponent: React.FC<PetEditDetailsProps> = ({
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  setFieldValue,
  submitCount,
  petId,
}) => {
  const router = useRouter();
  const {
    formGroup,
    formGroupDropDown,
    weightContainer,
    weightInput,
    weightUnitDropdown,
    verticalDivider,
    searchContainer,
    searchInput,
    referralContainer,
    qrCode,
    codeInput,
    inputRow
  } = styles;

  const { textInput } = INPUT_STYLES;

  return (
    <>
      {/* Pet's name */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Pet's name
          <TextTypes type='h5' color={COLOURS.primary}>
            *
          </TextTypes>
        </TextTypes>
        <TextInput
          style={textInput}
          placeholder='Enter pet name'
          value={values.name}
          onChangeText={handleChange('name')}
          onBlur={handleBlur('name')}
          placeholderTextColor={COLOURS.grayIcon}
        />
        {(touched.name || submitCount > 0) && errors.name && (
          <TextTypes type='errorText' color={COLOURS.errorText}>
            {errors.name}
          </TextTypes>
        )}
      </View>

      {/* Species */}
      <View style={formGroupDropDown}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Species
          <TextTypes type='h5' color={COLOURS.primary}>
            *
          </TextTypes>
        </TextTypes>
        <ModalDropdown
          placeholder='Select species'
          value={SPECIES.map((s) => ({ label: s, value: s })).find(s => s.value === values.species)}
          onChange={(item) => {
            setFieldValue('species', item.value);
            if (item.value !== values.species) {
              setFieldValue('breed', '');
            }
          }}
          options={SPECIES.map((s) => ({ label: s, value: s }))}
        />
        {(touched.species || submitCount > 0) && errors.species && (
          <TextTypes type='errorText' color={COLOURS.errorText}>
            {errors.species}
          </TextTypes>
        )}
      </View>

      {/* Breed */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Breed
        </TextTypes>
        <TextInput
          style={textInput}
          placeholder='Type breed'
          value={values.breed}
          onChangeText={handleChange('breed')}
          onBlur={handleBlur('breed')}
          placeholderTextColor={COLOURS.grayIcon}
        />
      </View>

      {/* Pet's sex */}
      <View style={formGroupDropDown}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Pet's sex
          <TextTypes type='h5' color={COLOURS.primary}>
            *
          </TextTypes>
        </TextTypes>
        <ModalDropdown
          placeholder='Select sex'
          value={SEXES.map((s) => ({ label: s, value: s })).find(s => s.value.toLowerCase() === values.gender.toLowerCase() )}
          onChange={(item) => setFieldValue('gender', item.value)}
          options={SEXES.map((s) => ({ label: s, value: s }))}
        />
        {(touched.gender || submitCount > 0) && errors.gender && (
          <TextTypes type='errorText' color={COLOURS.errorText}>
            {errors.gender}
          </TextTypes>
        )}
      </View>

      {/* Pet's weight */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Pet's weight
        </TextTypes>
        <View style={weightContainer}>
          <TextInput
            style={weightInput}
            placeholder='10'
            value={values.weight}
            onChangeText={handleChange('weight')}
            onBlur={handleBlur('weight')}
            placeholderTextColor={COLOURS.grayIcon}
            keyboardType='numeric'
          />
          <View style={verticalDivider} />
          <ModalDropdown
            placeholder='kg'
            value={WEIGHT_UNITS.map((u) => ({ label: u, value: u })).find(u => u.value === values.weightUnit)}
            onChange={(item) => setFieldValue('weightUnit', item.value)}
            options={WEIGHT_UNITS.map((u) => ({ label: u, value: u }))}
            customStyle={weightUnitDropdown}
            inline={true}
          />
        </View>
      </View>

      {/* Date of birth */}
      <ModalDatePicker
        label='Date of birth (or approx)'
        value={values.dateOfBirth ? new Date(values.dateOfBirth) : undefined}
        onChange={(date) => setFieldValue('dateOfBirth', date?.toISOString())}
        placeholder='19/02/2025'
      />

      {/* Microchip number */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Microchip number
        </TextTypes>
        <TextInput
          style={textInput}
          placeholder='Enter microchip number'
          value={values.microchipNumber}
          onChangeText={handleChange('microchipNumber')}
          onBlur={handleBlur('microchipNumber')}
          placeholderTextColor={COLOURS.grayIcon}
        />
      </View>

      {/* Insurance provider */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Insurance provider
        </TextTypes>
        <TextInput
          style={textInput}
          placeholder='Type to select insurance provider'
          value={values.insuranceProvider}
          onChangeText={handleChange('insuranceProvider')}
          onBlur={handleBlur('insuranceProvider')}
          placeholderTextColor={COLOURS.grayIcon}
        />
      </View>

      {/* Insurance policy number */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Insurance policy number
        </TextTypes>
        <TextInput
          style={textInput}
          placeholder='Enter insurance number'
          value={values.insurancePolicyNumber}
          onChangeText={handleChange('insurancePolicyNumber')}
          onBlur={handleBlur('insurancePolicyNumber')}
          placeholderTextColor={COLOURS.grayIcon}
        />
      </View>

      {/* Vet practice */}
      <View style={formGroup}>
        <TextTypes type='h5' color={COLOURS.textBlack}>
          Vet practice
        </TextTypes>
        <View style={searchContainer}>
          <SearchIcon width={20} height={20} color={COLOURS.textBlack} />
          <TextInput
            style={searchInput}
            placeholder='Search by name or postcode'
            placeholderTextColor={COLOURS.grayIcon}
          />
        </View>

        <View style={referralContainer}>
          <TextTypes type='h5' color={COLOURS.secondaryTint}>
            or
          </TextTypes>
        </View>

        <View style={referralContainer}>
          <TextTypes type='h5' color={COLOURS.secondaryTint}>
            want to link your vet?
          </TextTypes>
        </View>

        <View style={inputRow}>
          <TextInput
            style={[INPUT_STYLES.textInput, codeInput]}
            placeholder='Enter your code'
            placeholderTextColor={COLOURS.greyDark}
            autoCapitalize='characters'
            value={values.referralCode}
            onChangeText={handleChange('referralCode')}
            onBlur={handleBlur('referralCode')}
          />
          <TouchableOpacity
            activeOpacity={0.8}
            style={qrCode}
            onPress={() =>
              router.push({
                pathname: '/register/qrscan',
                params: { from: 'petEdit', petId: petId || '' },
              })
            }
            disabled={false}
          >
            <QRCode />
          </TouchableOpacity>
        </View>

        <TextTypes type='small' color={COLOURS.grayIcon}>
          Provided by your vet, insurer or group
        </TextTypes>

        {errors.referralCode && touched.referralCode && (
          <TextTypes type='small' color={COLOURS.errorText}>
            {errors.referralCode}
          </TextTypes>
        )}
      </View>
    </>
  );
};

export default PetEditDetailsComponent;
